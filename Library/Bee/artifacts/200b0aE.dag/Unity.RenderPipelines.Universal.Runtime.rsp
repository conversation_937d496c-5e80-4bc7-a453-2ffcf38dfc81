-target:library
-out:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll"
-refout:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-define:UNITY_6000_0_46
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_OSX
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_OSX
-define:UNITY_STANDALONE
-define:ENABLE_GAMECENTER
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:ENABLE_SPATIALTRACKING
-define:PLATFORM_HAS_CUSTOM_MUTEX
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:ENABLE_BURST_1_0_0_OR_NEWER
-define:ENABLE_VR_MODULE
-define:ENABLE_XR_MODULE
-define:USING_ANIMATION_MODULE
-define:USING_PHYSICS2D_MODULE
-define:ENABLE_INPUT_SYSTEM_PACKAGE
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/iOSSupport/UnityEditor.Apple.Extensions.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting@b4d700247d4b/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@b4d700247d4b/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@b4d700247d4b/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@b4d700247d4b/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/AssemblyInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ComponentUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Data/PostProcessData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Data/RenderStateData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Data/UniversalRenderPipelineAsset.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Data/UniversalRenderPipelineAsset.DefaultResources.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Data/UniversalRenderPipelineAssetPrefiltering.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Debug/DebugDisplaySettingsCommon.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Debug/DebugDisplaySettingsLighting.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Debug/DebugDisplaySettingsMaterial.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Debug/DebugDisplaySettingsRendering.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Debug/DebugHandler.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Debug/DebugRenderSetup.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Debug/UniversalRenderPipelineDebugDisplaySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Debug/UniversalRenderPipelineDebugDisplayStats.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Debug/UniversalRenderPipelineVolumeDebugSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Debug/UniversalRenderPipelineVolumeDebugSettings.deprecated.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/DBuffer/DBufferDepthCopyPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/DBuffer/DBufferRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/DBuffer/DecalForwardEmissivePass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/DecalDrawErrorRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/DecalPreviewPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/DecalProjector.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/DecalShaderPassNames.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/Entities/DecalChunk.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/Entities/DecalCreateDrawCallSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/Entities/DecalDrawSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/Entities/DecalEntityManager.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/Entities/DecalSkipCulledSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/Entities/DecalUpdateCachedSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/Entities/DecalUpdateCulledSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/Entities/DecalUpdateCullingGroupSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/ScreenSpace/DecalGBufferRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Decal/ScreenSpace/DecalScreenSpaceRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/DeferredLights.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Deprecated.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Documentation.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ForwardLights.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ForwardRendererData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/FrameData/Universal2DResourceData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/FrameData/UniversalCameraData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/FrameData/UniversalLightData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/FrameData/UniversalPostProcessingData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/FrameData/UniversalRenderingData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/FrameData/UniversalResourceBase.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/FrameData/UniversalResourceData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/FrameData/UniversalShadowData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/History/RawColorHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/History/RawDepthHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/History/SingleHistoryBase.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/History/StpHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/History/TaaHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/IntermediateTextureMode.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/LightCookieManager.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Memory/BuddyAllocator.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Memory/Fixed2.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Memory/PinnedArray.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/MotionVectors.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/NativeRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/NoAllocUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/NormalReconstruction.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/Bloom.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/ChannelMixer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/ChromaticAberration.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/ColorAdjustments.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/ColorCurves.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/ColorLookup.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/DepthOfField.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/FilmGrain.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/LensDistortion.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/LiftGammaGain.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/MotionBlur.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/PaniniProjection.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/ScreenSpaceLensFlare.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/ShadowsMidtonesHighlights.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/SplitToning.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/Tonemapping.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/Vignette.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Overrides/WhiteBalance.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/AdditionalLightsShadowAtlasLayout.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/AdditionalLightsShadowCasterPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/CapturePass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/ColorGradingLutPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/CopyColorPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/CopyDepthPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/DeferredPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/DepthNormalOnlyPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/DepthOnlyPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/DrawObjectsPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/DrawScreenSpaceUIPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/DrawSkyboxPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/FinalBlitPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/GBufferPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/HDRDebugViewPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/InvokeOnRenderObjectCallbackPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/MainLightShadowCasterPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/MotionVectorRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/PostProcessPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/PostProcessPassRenderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/ProbeVolumeDebugPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/RenderObjectsPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/ScreenSpaceAmbientOcclusionPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/ScriptableRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/TransparentSettingsPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/XRDepthMotionPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Passes/XROcclusionMeshPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/PostProcessPasses.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/PostProcessUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ReflectionProbeManager.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RendererFeatures/DecalRendererFeature.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RendererFeatures/DisallowMultipleRendererFeature.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RendererFeatures/FullScreenPassRendererFeature.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RendererFeatures/FullScreenPassRendererFeature.migration.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RendererFeatures/FullScreenPassRendererFeature_OldGUID.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RendererFeatures/RenderObjects.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RendererFeatures/ScreenSpaceAmbientOcclusion.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RendererFeatures/ScreenSpaceShadows.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderGraph/RenderGraphGraphicsAutomatedTests.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderingLayerUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderingUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderPipelineResources/Renderer2DResources.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderPipelineResources/UniversalRendererResources.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderPipelineResources/UniversalRenderPipelineDebugShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderPipelineResources/UniversalRenderPipelineEditorAssets.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderPipelineResources/UniversalRenderPipelineEditorMaterials.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderPipelineResources/UniversalRenderPipelineEditorShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderPipelineResources/UniversalRenderPipelineRuntimeShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderPipelineResources/UniversalRenderPipelineRuntimeTextures.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderPipelineResources/UniversalRenderPipelineRuntimeXRResources.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderTargetBufferSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RenderTargetHandle.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/RTHandleUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/SampleCount.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/SceneViewDrawMode.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ScriptableRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ScriptableRendererData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ScriptableRendererFeature.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Settings/RenderGraphSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Settings/URPDefaultVolumeProfileSetting.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Settings/URPShaderStrippingSetting.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ShaderBitArray.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ShaderData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ShaderUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ShadowCulling.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/ShadowUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/SpaceFillingCurves.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/StencilUsage.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/StpUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/SupportedOnRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/TemporalAA.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Tiling/InclusiveRange.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Tiling/LightMinMaxZJob.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Tiling/ReflectionProbeMinMaxZJob.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Tiling/TileRangeExpansionJob.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Tiling/TileSize.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Tiling/TilingJob.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/Tiling/ZBinningJob.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalAdditionalCameraData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalAdditionalLightData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalCameraHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalRendererData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalRendererDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalRendererRenderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalRenderPipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalRenderPipelineCore.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalRenderPipelineGlobalSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/UniversalRenderPipelineRenderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/VFXGraph/Utility/PropertyBinders/URPCameraBinder.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/VolumeRequiresRendererFeatures.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/XR/XRPassUniversal.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Runtime/XR/XRSystemUniversal.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"