-target:library
-out:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll"
-refout:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.ref.dll"
-define:UNITY_6000_0_46
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_OSX
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_OSX
-define:UNITY_STANDALONE
-define:ENABLE_GAMECENTER
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:ENABLE_SPATIALTRACKING
-define:PLATFORM_HAS_CUSTOM_MUTEX
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:ENABLE_XR_MODULE
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/iOSSupport/UnityEditor.Apple.Extensions.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting@b4d700247d4b/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@b4d700247d4b/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@b4d700247d4b/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@b4d700247d4b/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/CinemachineUniversalPixelPerfectEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/CompositeShadowCaster2DEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Converter/BuiltInToURP2DConverterContainer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Converter/BuiltInToURP2DMaterialUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Converter/ParametricToFreeformLightUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Converter/UpgradeURP2DAssetsContainer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Converter/URP2DConverterUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/FreeformPathPresets.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/GameObjectCreation.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Light2DEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Light2DEditorUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/LightBatchingDebugger/LightBatchingDebugger.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/PixelPerfectCameraEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Renderer2DAnalytics.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Renderer2DDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Renderer2DMenus.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteCustomLitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteLitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteUnlitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShaderGraph/Nodes/LightTextureNode.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShaderGraph/Targets/SpriteSubTargetUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShaderGraph/Targets/UniversalSpriteCustomLitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShaderGraph/Targets/UniversalSpriteLitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShaderGraph/Targets/UniversalSpriteUnlitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Shadows/CastingSourceDropDown.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Shadows/ShadowCaster2DEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Shadows/ShadowCaster2DShapeTool.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Shadows/ShadowProvider/ShadowShape2DProvider_ProperyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/Shadows/ShadowProvider/ShadowShape2DProvider_SpriteSkin_PropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/BezierUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/ControlPoint.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/EditablePath.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/EditablePathController.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/EditablePathExtensions.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/EditablePathUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/IEditablePath.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/IEditablePathController.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/ISnapping.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/IUndoObject.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/MultipleEditablePathController.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditablePath/Snapping.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditorTool/GenericScriptablePath.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditorTool/GenericScriptablePathInspector.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditorTool/PathComponentEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditorTool/PathEditorTool.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditorTool/PathEditorToolExtensions.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditorTool/ScriptableData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditorTool/ScriptablePath.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/EditorTool/ScriptablePathInspector.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/ClickAction.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/CommandAction.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/Control.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/DefaultControl.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/GenericControl.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/GenericDefaultControl.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/GUIAction.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/GUIState.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/GUISystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/HoveredControlAction.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/IGUIState.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/LayoutData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/GUIFramework/SliderAction.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Selection/IndexedSelection.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Selection/ISelectable.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Selection/ISelection.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Selection/ISelector.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Selection/PointRectSelector.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Selection/RectSelector.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Selection/SerializableSelection.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Shapes/IShape.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Shapes/Polygon.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Shapes/ShapeExtensions.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/Shapes/Spline.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/View/CreatePointAction.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/View/Drawer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/View/EditablePathView.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/View/IDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/ShapeEditor/View/IEditablePathView.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/2D/SortingLayerDropDown.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Analytics/AssetReimporterAnalytic.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Analytics/PostProcessDataAnalytics.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AnimationClipUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AnimationClipUpgrader_Types.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssemblyInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetPostProcessors/AutodeskInteractiveMaterialImport.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetPostProcessors/FBXArnoldSurfaceMaterialDescriptionPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetPostProcessors/FBXMaterialDescriptionPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetPostProcessors/MaterialPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetPostProcessors/ModelPostProcessor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetPostProcessors/PhysicalMaterial3DsMaxPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetPostProcessors/ShaderGraphMaterialsUpdater.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetPostProcessors/SketchupMaterialDescriptionPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetPostProcessors/ThreeDSMaterialDescriptionPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetPostProcessors/UniversalRenderPipelineGlobalSettingsPostprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/AssetVersion.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/BuildProcessors/GraphicsSettingsStrippers/RendererStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/BuildProcessors/URPBuildData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/BuildProcessors/URPBuildDataValidator.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/BuildProcessors/URPPreprocessBuild.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/BuildProcessors/URPProcessScene.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraUI.Environment.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraUI.Environment.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraUI.Output.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraUI.Output.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraUI.PhysicalCamera.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraUI.PresetInspector.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraUI.Rendering.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraUI.Rendering.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineCameraUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Camera/UniversalRenderPipelineSerializedCamera.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ContextualMenuDispatcher.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/AnimationClipConverter.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/BuiltInToURPConverterContainer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/ConversionIndexers.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/ConverterItemDescriptor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/ConverterItemInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/Converters.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/Converters/RenderSettingsConverter.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/InitializeConverterContext.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/MaterialReferenceBuilder.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/ReadonlyMaterialConverter.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/RenderPipelineConverter.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/RenderPipelineConverterContainer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/RenderPipelineConvertersEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Converter/RunItemContext.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Decal/CreateDecalProjector.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Decal/CreateDecalShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Decal/DecalProjectorEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Decal/DecalProjectorEditor.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Decal/DecalShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Decal/DisplacableRectHandles.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Decal/ProjectedTransform.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/DefaultScene/UniversalProjectSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Deprecated.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/EditorUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/LightExplorer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Lighting/UniversalRenderPipelineLightUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Lighting/UniversalRenderPipelineLightUI.PresetInspector.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Lighting/UniversalRenderPipelineLightUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Lighting/UniversalRenderPipelineSerializedLight.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/BloomEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/ChannelMixerEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/ColorCurvesEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/ColorLookupEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/DepthOfFieldEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/FilmGrainEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/LiftGammaGainEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/MotionBlurEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/ScreenSpaceLensFlareEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/ShadowsMidtonesHighlightsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Overrides/TonemappingEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/PostProcessDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/RendererFeatures/DecalRendererFeatureEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/RendererFeatures/FullScreenPassRendererFeatureEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/RendererFeatures/NewPostProcessTemplateDropdownItems.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/RendererFeatures/NewRendererFeatureDropdownItem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/RendererFeatures/RenderObjectsPassFeatureEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/RendererFeatures/ScreenSpaceAmbientOcclusionEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/RendererFeatures/ScreenSpaceShadowsEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/RenderStateDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/SavedParameter.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/SceneTemplates/URPBasicScenePipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ScriptableRendererDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ScriptableRendererFeatureProvider.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Settings/PropertyDrawers/URPDefaultVolumeProfileSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Settings/PropertyDrawers/URPRenderGraphPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/Settings/PropertyDrawers/URPShaderStrippingSettingsPropertyDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderBuildPreprocessor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/AssetCallbacks/CreateCanvasShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/AssetCallbacks/CreateFullscreenShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/AssetCallbacks/CreateLitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/AssetCallbacks/CreateSixWayShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/AssetCallbacks/CreateUnlitShaderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/Includes/DecalMeshBiasTypeEnum.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/Nodes/UniversalSampleBufferNode.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/Targets/UniversalCanvasSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/Targets/UniversalDecalSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/Targets/UniversalFullscreenSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/Targets/UniversalLitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/Targets/UniversalSixWaySubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/Targets/UniversalSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/Targets/UniversalTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/Targets/UniversalUnlitSubTarget.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/UniversalBlockFields.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/UniversalFields.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/UniversalMetadata.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/UniversalProperties.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/UniversalStructFields.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGraph/UniversalStructs.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/BaseShaderGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/ParticleGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/ShaderGraphLitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/ShadergraphSpriteGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/ShaderGraphUnlitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/Shaders/BakedLitShader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/Shaders/LitShader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/Shaders/ParticlesLitShader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/Shaders/ParticlesSimpleLitShader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/Shaders/ParticlesUnlitShader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/Shaders/SimpleLitShader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/Shaders/UnlitShader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/ShadingModels/BakedLitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/ShadingModels/LitDetailGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/ShadingModels/LitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/ShadingModels/SimpleLitGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/SixWayGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderGUI/TerrainLitShaderGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderScriptableStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderStripTool.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/ShaderUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/TrackballUIDrawer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalAdditionalCameraDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalAdditionalLightDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalAnalytics.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalRendererDataEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalRenderPipelineAsset/SerializedUniversalRenderPipelineAsset.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalRenderPipelineAsset/UniversalRenderPipelineAssetUI.Drawers.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalRenderPipelineAsset/UniversalRenderPipelineAssetUI.Skin.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalRenderPipelineAssetEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalRenderPipelineLightEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalRenderPipelineMaterialUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalRenderPipelineVolumeComponentEditor.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalSpeedTree8MaterialUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UniversalSpeedTree9MaterialUpgrader.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UpgradeCommon.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/UpgradeUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/VFXGraph/VFXAbstractParticleURPLitOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/VFXGraph/VFXDecalURPOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/VFXGraph/VFXShaderGraphGUI.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/VFXGraph/VFXURPBinder.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/VFXGraph/VFXURPLitMeshOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/VFXGraph/VFXURPLitPlanarPrimitiveOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/VFXGraph/VFXURPLitQuadStripOutput.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@18be219df6cb/Editor/VFXGraph/VFXURPSubOutput.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"