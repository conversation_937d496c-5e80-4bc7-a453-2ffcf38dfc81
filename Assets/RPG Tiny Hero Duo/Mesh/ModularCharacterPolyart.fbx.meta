fileFormatVersion: 2
guid: 14ab85c74a636c74baa3ae08a1b410bc
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable: []
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 0
    normalImportMode: 1
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: pelvis
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thigh_l
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thigh_r
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: calf_l
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: calf_r
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: foot_l
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: foot_r
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: spine_01
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: upperarm_l
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: upperarm_r
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: lowerarm_l
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: lowerarm_r
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: hand_l
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: hand_r
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ball_l
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ball_r
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_01_l
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_02_l
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_01_l
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_02_l
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_01_r
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_02_r
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_01_r
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_02_r
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: spine_02
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: spine_03
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: clavicle_l
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: clavicle_r
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: neck_01
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_03_l
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_03_l
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thumb_03_r
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: index_03_r
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Idle_Battle_SwordAndShiled(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hair01
      parentName: Idle_Battle_SwordAndShiled(Clone)
      position: {x: 5.5542004e-16, y: 0.9141078, z: 0.012847028}
      rotation: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
      scale: {x: 1, y: 1, z: 1}
    - name: Eye01
      parentName: Idle_Battle_SwordAndShiled(Clone)
      position: {x: 2.7120293e-16, y: 0.9141078, z: 0.012847028}
      rotation: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
      scale: {x: 1, y: 1, z: 1}
    - name: Head01_Male
      parentName: Idle_Battle_SwordAndShiled(Clone)
      position: {x: 2.7120293e-16, y: 0.9141078, z: 0.012847028}
      rotation: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
      scale: {x: 1, y: 1, z: 1}
    - name: Mouth01
      parentName: Idle_Battle_SwordAndShiled(Clone)
      position: {x: 5.5542004e-16, y: 0.9141078, z: 0.012847028}
      rotation: {x: -0.5, y: 0.5, z: -0.5, w: 0.5}
      scale: {x: 1, y: 1, z: 1}
    - name: Body05
      parentName: Idle_Battle_SwordAndShiled(Clone)
      position: {x: -0, y: 0.45777598, z: -0.45902124}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: Cloak02
      parentName: Idle_Battle_SwordAndShiled(Clone)
      position: {x: -0, y: 0.5790208, z: -0.81771773}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: root
      parentName: Idle_Battle_SwordAndShiled(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7093532, y: -0, z: -0, w: 0.7048532}
      scale: {x: 1, y: 1.0000006, z: 1.0000006}
    - name: pelvis
      parentName: root
      position: {x: -1.0845211e-17, y: 0.00007575201, z: 0.32165092}
      rotation: {x: 0.062176764, y: 0.7043679, z: -0.062176853, w: 0.7043679}
      scale: {x: 1.0000018, y: 1.0000011, z: 1.0000014}
    - name: thigh_l
      parentName: pelvis
      position: {x: 0.029832672, y: -0.0141515955, z: -0.09502096}
      rotation: {x: -0.036186893, y: 0.03976303, z: -0.002768956, w: 0.9985498}
      scale: {x: 1.0000007, y: 1.0000006, z: 1.0000004}
    - name: calf_l
      parentName: thigh_l
      position: {x: 0.1377729, y: -0.0059484076, z: -0.0027491562}
      rotation: {x: 0.02691302, y: -0.022706516, z: 0.0010662572, w: -0.99937934}
      scale: {x: 1.0000007, y: 1.0000001, z: 0.99999994}
    - name: foot_l
      parentName: calf_l
      position: {x: 0.12562445, y: 0.012257197, z: 0.002024065}
      rotation: {x: -0.025008963, y: -0.0624419, z: 0.0012533205, w: 0.9977344}
      scale: {x: 1.0000008, y: 1.0000015, z: 1.0000015}
    - name: ball_l
      parentName: foot_l
      position: {x: 0.020222384, y: -0.10951788, z: -0.0015459505}
      rotation: {x: -0.00000006146728, y: -0.000000048428767, z: -0.7071068, w: -0.7071068}
      scale: {x: 1.0000011, y: 0.9999998, z: 1.0000013}
    - name: spine_01
      parentName: pelvis
      position: {x: -0.09894391, y: -0.01896602, z: 0.0033746792}
      rotation: {x: -0.08780647, y: 0.004682541, z: 0.053046927, w: 0.9947131}
      scale: {x: 0.99999946, y: 0.9999992, z: 0.999999}
    - name: spine_02
      parentName: spine_01
      position: {x: -0.107710324, y: 0.011222288, z: 6.8414275e-17}
      rotation: {x: 0.000000022351742, y: -0.00000016344711, z: 0.1341374, w: -0.9909628}
      scale: {x: 0.9999996, y: 1.0000002, z: 1.0000002}
    - name: spine_03
      parentName: spine_02
      position: {x: -0.09346745, y: 8.8817837e-17, z: 6.418694e-17}
      rotation: {x: -0.000000011051662, y: -0.000000081724345, z: 0.042781133, w: 0.9990845}
      scale: {x: 1.0000001, y: 1.0000006, z: 0.99999994}
    - name: neck_01
      parentName: spine_03
      position: {x: -0.07696576, y: -1.5099033e-16, z: 1.13396255e-17}
      rotation: {x: 0.000000013483575, y: 0.000000017281502, z: -0.087512165, w: -0.9961635}
      scale: {x: 1.0000019, y: 1.0000018, z: 1.0000014}
    - name: head
      parentName: neck_01
      position: {x: -0.20523393, y: -3.5527136e-17, z: 1.09031954e-16}
      rotation: {x: -0.000000025381734, y: -0.000000052992352, z: 0.045955647, w: -0.99894345}
      scale: {x: 1.0000007, y: 1.0000011, z: 1.0000013}
    - name: clavicle_l
      parentName: spine_03
      position: {x: -0.01944315, y: -0.021992614, z: -0.062890135}
      rotation: {x: -0.10418874, y: 0.7700338, z: 0.0322023, w: -0.6286141}
      scale: {x: 1.0000005, y: 1.0000021, z: 1.0000014}
    - name: upperarm_l
      parentName: clavicle_l
      position: {x: -0.10173372, y: 4.440892e-18, z: -1.4210854e-16}
      rotation: {x: -0.011902688, y: 0.10369653, z: 0.070935935, w: 0.99200475}
      scale: {x: 1.0000002, y: 1.0000017, z: 1.0000025}
    - name: lowerarm_l
      parentName: upperarm_l
      position: {x: -0.19922544, y: -1.9984014e-17, z: 2.1316282e-16}
      rotation: {x: 0.000000054948018, y: -0.000000029802315, z: 0.058328252, w: 0.9982975}
      scale: {x: 1.0000004, y: 1.0000006, z: 1.0000005}
    - name: hand_l
      parentName: lowerarm_l
      position: {x: -0.13859957, y: -3.5527136e-17, z: -1.065814e-15}
      rotation: {x: 0.70662516, y: -0.026095154, z: 0.02609507, w: -0.7066251}
      scale: {x: 1.0000001, y: 1.0000031, z: 1.0000025}
    - name: index_01_l
      parentName: hand_l
      position: {x: -0.078995824, y: -0.029177444, z: 0.005036025}
      rotation: {x: -0.0000028908248, y: 0.0025714093, z: 0.0010916347, w: 0.9999962}
      scale: {x: 1.0000007, y: 1.0000012, z: 1.0000017}
    - name: index_02_l
      parentName: index_01_l
      position: {x: -0.084993236, y: -2.842171e-16, z: -2.8366196e-16}
      rotation: {x: 0.000005745178, y: -0.0034163173, z: -0.040483348, w: 0.99917436}
      scale: {x: 1.0000007, y: 1.0000042, z: 1.0000045}
    - name: index_03_l
      parentName: index_02_l
      position: {x: -0.063433915, y: -4.2632563e-16, z: 2.7755574e-18}
      rotation: {x: -0.0026570698, y: 0.010807825, z: -0.035711274, w: 0.99930024}
      scale: {x: 0.99999946, y: 1.0000002, z: 0.99999994}
    - name: thumb_01_l
      parentName: hand_l
      position: {x: -0.062028673, y: 0.025731737, z: -0.046623435}
      rotation: {x: -0.5024613, y: 0.3625928, z: -0.14099312, w: -0.77212703}
      scale: {x: 1.0000008, y: 1.0000013, z: 1.0000001}
    - name: thumb_02_l
      parentName: thumb_01_l
      position: {x: -0.052380655, y: -4.6629366e-17, z: 0}
      rotation: {x: -0.024324644, y: -0.023914147, z: -0.032934725, w: 0.9988753}
      scale: {x: 1.0000007, y: 1.0000002, z: 1.000003}
    - name: thumb_03_l
      parentName: thumb_02_l
      position: {x: -0.045099992, y: 1.7763568e-17, z: -1.4210854e-16}
      rotation: {x: -0.012497648, y: -0.0024275442, z: -0.071094766, w: 0.9973884}
      scale: {x: 1.0000021, y: 0.99999994, z: 1.0000017}
    - name: weapon_l
      parentName: hand_l
      position: {x: -0.094, y: 0.0069999998, z: 0.004}
      rotation: {x: -0.7071067, y: 0.0000000018626445, z: 0.00000002833446, w: -0.7071068}
      scale: {x: 1.0000005, y: 1.0000007, z: 1.0000005}
    - name: shoulderPadJoint_l
      parentName: clavicle_l
      position: {x: -0.10172261, y: 0.0000031123172, z: 0.000054128275}
      rotation: {x: -0.009676, y: 0.10392798, z: 0.09219542, w: 0.99025524}
      scale: {x: 1.0000008, y: 1.0000015, z: 1.0000021}
    - name: CloakBone01
      parentName: spine_03
      position: {x: -0.07525502, y: 0.1138894, z: -1.3765184e-16}
      rotation: {x: -0.19011138, y: 0.9817625, z: -0.000000034166476, w: -0.00000002543333}
      scale: {x: 1.0000001, y: 1.0000014, z: 1.000001}
    - name: CloakBone02
      parentName: CloakBone01
      position: {x: -0.2614295, y: 2.6645352e-17, z: -5.804901e-17}
      rotation: {x: -0.000000022111164, y: -0.000000037080298, z: 0.09271763, w: 0.9956925}
      scale: {x: 1.0000005, y: 0.9999998, z: 0.9999997}
    - name: CloakBone03
      parentName: CloakBone02
      position: {x: -0.30492613, y: -1.0658141e-16, z: -7.123106e-17}
      rotation: {x: -0.000000019613275, y: 0.00000004588284, z: 0.0567417, w: 0.99838895}
      scale: {x: 1.0000001, y: 0.99999934, z: 0.99999946}
    - name: BackpackBone
      parentName: spine_03
      position: {x: -0.05273138, y: 0.21801382, z: -1.2915617e-16}
      rotation: {x: -0.02944411, y: 0.7064935, z: -0.029444126, w: -0.7064935}
      scale: {x: 1.0000006, y: 1.0000011, z: 1.0000013}
    - name: clavicle_r
      parentName: spine_03
      position: {x: -0.01944353, y: -0.021992618, z: 0.0628901}
      rotation: {x: -0.7700337, y: -0.104188755, z: -0.6286142, w: -0.032202322}
      scale: {x: 1.0000011, y: 1.0000011, z: 1.0000007}
    - name: upperarm_r
      parentName: clavicle_r
      position: {x: 0.10173414, y: 0.000000103064025, z: 0.000000544563}
      rotation: {x: -0.011902684, y: 0.10369647, z: 0.070935935, w: 0.9920048}
      scale: {x: 0.9999997, y: 1.0000011, z: 1.0000002}
    - name: lowerarm_r
      parentName: upperarm_r
      position: {x: 0.19922526, y: -1.1510433e-11, z: 2.5579537e-15}
      rotation: {x: 0.00000006891785, y: -0.000000014901156, z: 0.058328275, w: 0.99829745}
      scale: {x: 1.0000002, y: 1.0000013, z: 1.0000031}
    - name: hand_r
      parentName: lowerarm_r
      position: {x: 0.13859951, y: 9.530944e-10, z: 1.563194e-15}
      rotation: {x: -0.7066251, y: 0.026095143, z: -0.02609511, w: 0.7066251}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.000001}
    - name: index_01_r
      parentName: hand_r
      position: {x: 0.078995995, y: 0.029178, z: -0.00503602}
      rotation: {x: -0.000003010034, y: 0.0025714354, z: 0.0010915379, w: 0.9999962}
      scale: {x: 1.0000001, y: 0.99999994, z: 0.99999994}
    - name: index_02_r
      parentName: index_01_r
      position: {x: 0.084993325, y: -0.00000055632756, z: 6.6904715e-10}
      rotation: {x: 0.000005959391, y: -0.0034166572, z: -0.04048517, w: 0.9991743}
      scale: {x: 0.99999994, y: 1.0000006, z: 0.9999998}
    - name: index_03_r
      parentName: index_02_r
      position: {x: 0.063433595, y: -0.00000008902263, z: -0.0000000019323867}
      rotation: {x: -0.002657218, y: 0.0108078215, z: -0.035711385, w: 0.99930024}
      scale: {x: 1.0000004, y: 1.0000006, z: 0.99999946}
    - name: thumb_01_r
      parentName: hand_r
      position: {x: 0.062028997, y: -0.025731001, z: 0.046623457}
      rotation: {x: 0.50245965, y: -0.36258844, z: 0.14099674, w: 0.77212954}
      scale: {x: 1.0000006, y: 1, z: 1.0000012}
    - name: thumb_02_r
      parentName: thumb_01_r
      position: {x: 0.052380603, y: -0.000000021094301, z: 0.00000059586444}
      rotation: {x: -0.024324942, y: -0.023915488, z: -0.032934904, w: 0.99887526}
      scale: {x: 1, y: 0.9999992, z: 1.0000019}
    - name: thumb_03_r
      parentName: thumb_02_r
      position: {x: 0.045099515, y: -0.00000023024785, z: 0.000000114253595}
      rotation: {x: -0.012497454, y: -0.002427339, z: -0.07109481, w: 0.99738836}
      scale: {x: 1.0000015, y: 0.9999998, z: 1.0000014}
    - name: weapon_r
      parentName: hand_r
      position: {x: 0.09399239, y: -0.0072682807, z: -0.00360484}
      rotation: {x: 0.70710677, y: 0.000000013038514, z: -0.0000000035533598, w: 0.70710695}
      scale: {x: 1.0000002, y: 1.0000006, z: 1.0000007}
    - name: shoulderPadJoint_r
      parentName: clavicle_r
      position: {x: 0.10172303, y: -0.0000030092533, z: -0.000053583713}
      rotation: {x: -0.99025524, y: -0.09219543, z: 0.10392789, w: -0.009675993}
      scale: {x: 1.0000001, y: 1.000002, z: 1.0000015}
    - name: thigh_r
      parentName: pelvis
      position: {x: 0.02983256, y: 0.019493982, z: 0.094070405}
      rotation: {x: -0.03963389, y: 0.1393248, z: 0.9894442, w: -0.004238036}
      scale: {x: 1.0000002, y: 0.9999998, z: 0.9999998}
    - name: calf_r
      parentName: thigh_r
      position: {x: -0.13777272, y: 0.0059483806, z: 0.0027488763}
      rotation: {x: -0.026912972, y: 0.022706501, z: -0.0010662051, w: 0.9993793}
      scale: {x: 1.0000015, y: 1.0000001, z: 1.0000001}
    - name: foot_r
      parentName: calf_r
      position: {x: -0.12562485, y: -0.012257216, z: -0.0020234145}
      rotation: {x: -0.025034029, y: -0.062442236, z: 0.0012543072, w: 0.99773383}
      scale: {x: 1.0000001, y: 1.0000008, z: 1.000001}
    - name: ball_r
      parentName: foot_r
      position: {x: -0.020222299, y: 0.109517895, z: 0.0015459999}
      rotation: {x: -0.00000007823109, y: -0.0000000372529, z: 0.70710677, w: 0.70710695}
      scale: {x: 1.0000015, y: 1.000005, z: 1.0000027}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 1
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 0308cf4e83cf517488b60af58b290fe0,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 2
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 225148
  packageName: RPG Tiny Hero Duo PBR Polyart
  packageVersion: 2.0
  assetPath: Assets/RPG Tiny Hero Duo/Mesh/ModularCharacterPolyart.fbx
  uploadId: 521528
