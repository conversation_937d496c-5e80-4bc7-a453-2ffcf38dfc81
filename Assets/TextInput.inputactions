{"version": 1, "name": "TextInput", "maps": [{"name": "Player", "id": "beb8bad9-7702-43b1-9008-ff05ace6e0d3", "actions": [{"name": "Move", "type": "Value", "id": "3a74b3b3-90e4-42f1-a600-ee019ff993ff", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "c1b32976-2bf1-4248-80fe-6490a12b9a2a", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "WASD", "id": "e5d54acd-7ceb-47f0-9d6b-a5127acbb077", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "26a4e891-99fb-4f41-a1cf-c34d8e7adf0a", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "9d1ef8df-aaa7-4a7b-b4aa-01343816d89d", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "7f62a717-7142-4822-bdb8-94abe4dc87ed", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "e34d912c-e382-4ffc-8a0d-a5a5445addab", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "eac69123-2022-40fa-81a3-8c47913b74b7", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "", "action": "Jump", "isComposite": false, "isPartOfComposite": false}]}, {"name": "UI", "id": "96a641c3-8b3e-43a7-9c22-d8c5329f462e", "actions": [{"name": "Navigate", "type": "PassThrough", "id": "afca9970-b8c4-48a6-8508-d867e0c01afd", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Submit", "type": "<PERSON><PERSON>", "id": "ffe4ec93-89bb-4509-95ee-028f4b128e2f", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Cancel", "type": "<PERSON><PERSON>", "id": "fa9d44ab-00a1-43d1-ab19-d2e3e3453ed8", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Point", "type": "PassThrough", "id": "daf67cce-eb6e-4c6b-b9c6-6d771bc9f5ab", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Click", "type": "PassThrough", "id": "f74c4234-9c74-4079-a16d-9b7969c85194", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "RightClick", "type": "PassThrough", "id": "ba15586a-d436-42e8-a80f-4df1b142f18e", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "MiddleClick", "type": "PassThrough", "id": "75018786-2cd8-44d7-9be5-832652c71bdf", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "ScrollWheel", "type": "PassThrough", "id": "c0dc6b0f-262e-4da1-b866-f995da6b7072", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDevicePosition", "type": "PassThrough", "id": "4a621e60-6767-4fbd-bbad-b378916c6166", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDeviceOrientation", "type": "PassThrough", "id": "5f053e79-1d2a-4802-846c-ff75ece1cc53", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "Gamepad", "id": "38e406d5-c398-4f18-88bc-60951588ac89", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "d14ce9f3-53e9-4a4d-9f39-5e0c14d9f041", "path": "<Gamepad>/leftStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "54a3c8bc-d0a6-4f6f-81b9-9165fdebb523", "path": "<Gamepad>/rightStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "20517c64-42e7-4901-aa34-4cf38ea72538", "path": "<Gamepad>/leftStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "d177e63a-b67f-40d3-87e3-8a660cf2c3c5", "path": "<Gamepad>/rightStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "2e8db94d-4a91-424e-9e3b-584b19a76ae7", "path": "<Gamepad>/leftStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "12bf2df7-ba07-4687-bd43-69077789e57a", "path": "<Gamepad>/rightStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "198bfe1c-00c1-44e9-a633-01e4dd08ebe2", "path": "<Gamepad>/leftStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "071947b3-472d-4f7e-88bd-cf47434f589d", "path": "<Gamepad>/rightStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "7a1870ce-27ad-48c4-b301-f7926e442563", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "Joystick", "id": "3f08aee8-738d-4361-a679-852f6156e2b5", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "3be72e75-a561-4edb-950c-7bad0e83a805", "path": "<Joystick>/stick/up", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "29b15c83-23e2-4741-b5c1-96b726691d70", "path": "<Joystick>/stick/down", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "d62e5a2c-cc65-4451-9f2d-7dc0d71da6ef", "path": "<Joystick>/stick/left", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "54cd6f7e-2bb5-4ea2-83c9-ec27f117d612", "path": "<Joystick>/stick/right", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "Keyboard", "id": "8366b6ce-1456-4467-b81c-4ca17ea630f7", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "c0c27bd7-8b04-439c-9884-3fcd3a5a1b29", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "02e9f4b7-579a-4a71-9df8-0f8afe602875", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "abeebcc6-0605-48ca-b7d5-30127a194453", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "cc98bcbf-7f2f-478d-bcf9-45c8f19ca22c", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "3297c203-f914-44f8-a7ee-a9a205719672", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "61797322-6723-47a5-89e4-3470416ca6ba", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "80b5c6b0-9521-400e-a17c-b35395dd1901", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "65958b88-cf8f-458b-9fe7-05840a038fc2", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "85dbe6c2-91bb-4193-a3a4-69bf9862480b", "path": "*/{Submit}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;Gamepad;Touch;Joystick;XR", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c9cfb07d-f8bf-497b-8036-cbb4ecdd8c8e", "path": "*/{Cancel}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;Gamepad;Touch;Joystick;XR", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a31010a4-e047-4616-8e1c-255ef88c77e5", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3216bd5e-76a9-4245-9277-37dfc1c73794", "path": "<Pen>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "eb6c6348-1261-4c20-81b9-396d7e8a0bb6", "path": "<Touchscreen>/touch*/position", "interactions": "", "processors": "", "groups": "Touch", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8bf42ddd-cc73-4d1c-8700-64c6c2caf385", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0787a28b-5175-414e-b529-d90422b5d795", "path": "<Pen>/tip", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b2e881e6-5e3c-4a8f-85aa-678e40ea2038", "path": "<Touchscreen>/touch*/press", "interactions": "", "processors": "", "groups": "Touch", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d92a43d1-0700-4eb8-a9c4-a418820893f3", "path": "<XRController>/trigger", "interactions": "", "processors": "", "groups": "XR", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d4811db0-b5a5-41cd-a22a-a1c0cc6cb720", "path": "<Mouse>/scroll", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "ScrollWheel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "cd5aa5dc-3416-48ed-b565-f1101f4c494e", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "RightClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "aaea410e-211c-46ec-bf80-b254a0bef6de", "path": "<Mouse>/middleButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "MiddleClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5c8fc554-b7b7-4ddf-9263-df20a73fb3be", "path": "<XRController>/devicePosition", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDevicePosition", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "54a05344-d6b5-4806-8a3f-06b27b024c6c", "path": "<XRController>/deviceRotation", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDeviceOrientation", "isComposite": false, "isPartOfComposite": false}]}, {"name": "Scene", "id": "09b61bb1-d9a1-4ecb-aeb4-c930260055bd", "actions": [{"name": "SentMessage", "type": "<PERSON><PERSON>", "id": "d52e1386-d3c0-4349-8ba7-40b969690704", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "MouseClick", "type": "<PERSON><PERSON>", "id": "e8585dc0-d767-4c9c-b103-1204a6718018", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "57802a91-c736-4065-a4a7-202382f62686", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "", "action": "SentMessage", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5ebf1e37-b522-49a6-be8c-5d92b3d2f4ac", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": "", "action": "MouseClick", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": []}