using UnityEngine;

public class API04Component : MonoBehaviour
{
    void Start()
    {
        // // 获取当前游戏对象的类型
        // System.Type gameObjectType = this.gameObject.GetType();
        // Debug.Log("当前游戏对象类型: " + gameObjectType.Name);
        //
        // //设置当前游戏对象的tag，并打印
        // this.tag = "Player"; //等同等this.gameObject.tag = "Player";
        // Debug.Log("当前游戏对象的Tag: " + this.tag);

        //访问Transform组件，并设置其值
        this.transform.position = this.gameObject.GetComponentInParent<Transform>().transform.position + new Vector3(0, 1, 0);
    }

    public void PrintMessage()
    {
        Debug.Log("API04Component中的PrintMessage方法被调用");
    }
}
