using UnityEngine;

//按一定速度旋转物体
public class API08Mathf : MonoBehaviour
{
    [SerializeField] private float rotationSpeed = 30f; // 每秒旋转的角度
    [SerializeField] private float radius = 1f; // 圆周运动的半径
    [SerializeField] private float tiltAngleX = 45f; // X轴倾斜角度
    [SerializeField] private float tiltAngleY = 45f; // Y轴倾斜角度

    private float currentAngle = 0f; // 当前累积的角度
    private Vector3 centerPosition; // 圆心位置

    private void Start()
    {
        // 保存初始位置作为圆心
        centerPosition = GetComponentInParent<API08Mathf>().transform.position;
    }

    void Update()
    {
        // 累加当前帧的旋转角度
        currentAngle += rotationSpeed * Time.deltaTime;

        // 将角度转换为弧度
        float angleInRadians = currentAngle * Mathf.Deg2Rad;

        // 计算旋转后的位置（绕圆周运动）
        float x = Mathf.Cos(angleInRadians) * radius;
        float y = Mathf.Sin(angleInRadians) * radius;

        // 计算倾斜角度的弧度值
        float tiltXRad = tiltAngleX * Mathf.Deg2Rad;
        float tiltYRad = tiltAngleY * Mathf.Deg2Rad;

        // 通过旋转变换计算倾斜后新的位置
        float newY = y * Mathf.Cos(tiltXRad);
        float newZ = x * Mathf.Sin(tiltYRad) - y * Mathf.Sin(tiltXRad) * Mathf.Cos(tiltYRad);
        float newX = x * Mathf.Cos(tiltYRad) + y * Mathf.Sin(tiltXRad) * Mathf.Sin(tiltYRad);

        // 应用位置，以centerPosition为中心
        transform.position = new Vector3(newX, newY, newZ) + centerPosition;
    }
}
