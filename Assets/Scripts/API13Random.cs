using UnityEngine;
using UnityEngine.InputSystem;

public class API13Random : MonoBehaviour
{
    private Vector2 vector;
    private Vector3 centerTransform;
    private TextInput myInput;

    void OnEnable()
    {
        // 初始化自定义输入系统
        myInput ??= new TextInput();

        // 启用自定义输入系统
        myInput.Enable();

        // 注册事件
        myInput.Scene.SentMessage.performed += OnSentMessage;
    }

    void Start()
    {
        centerTransform = transform.position;
        print("当前位置：" + transform.position);
    }

    void OnDisable()
    {
        // 取消注册事件
        myInput.Scene.SentMessage.performed -= OnSentMessage;

        // 禁用自定义输入系统
        myInput.Disable();
    }


    void OnSentMessage(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            // 以游戏对象的默认位置为中心，随机生成位置
            vector = Random.insideUnitCircle * 5;

            // 更新游戏对象的位置
            transform.position = centerTransform + new Vector3(vector.x, 0, vector.y);
            Debug.Log("新位置：" + transform.position);
        }
    }
}
