using UnityEngine;
using UnityEngine.InputSystem;

public class API03GameObject : MonoBehaviour
{
    //声明自定义新输入系统
    private TextInput textInput;

    void Awake()
    {
        //初始化自定义的新输入系统
        textInput ??= new TextInput();
    }

    void OnEnable()
    {
        //启用新InputSystem
        textInput.Enable();

        // 给新输入系统注册事件
        textInput.Scene.SentMessage.performed += OnSendMessage;
    }

    void Disable()
    {
        if (textInput != null)
        {
            // 禁用新InputSystem
            textInput.Scene.SentMessage.performed -= OnSendMessage;
            textInput.Disable();
            Debug.Log("新输入系统已禁用");
        }
        else
        {
            Debug.LogWarning("textInput 未初始化，无法禁用");
        }
    }

    void Start()
    {
        // Debug.Log("API03GameObject 已启动");
        //
        // //给当前GameObject添加MessageReceiver组件
        // if (this.gameObject.GetComponent<MessageReceiver>() == null)
        // {
        //     this.gameObject.AddComponent<MessageReceiver>();
        //     Debug.Log("已为当前GameObject添加MessageReceiver组件");
        // }
        //
        // // 检查是否已有子对象
        // if (transform.childCount == 0)
        // {
        //     // 创建子对象并添加MessageReceiver组件
        //     GameObject child = new GameObject("ChildObject");
        //     child.transform.SetParent(transform);
        //     child.AddComponent<MessageReceiver>();
        //     Debug.Log("已创建子对象并添加MessageReceiver组件");
        // }
        // else
        // {
        //     // 为现有子对象添加MessageReceiver组件
        //     foreach (Transform child in transform)
        //     {
        //         if (child.GetComponent<MessageReceiver>() == null)
        //         {
        //             child.gameObject.AddComponent<MessageReceiver>();
        //             Debug.Log($"已为子对象 {child.name} 添加MessageReceiver组件");
        //         }
        //     }
        // }
    }


    // 当按下指定按键时调用
    void OnSendMessage(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            this.gameObject.SendMessageUpwards("ReceiveMessage", "你好，世界！", SendMessageOptions.DontRequireReceiver);
        }
    }
}

