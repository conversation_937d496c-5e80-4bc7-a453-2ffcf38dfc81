using UnityEngine;
using System.Collections;
using UnityEngine.InputSystem;

public class API06Coroutine : MonoBehaviour
{
    private TextInput txt; // 声明一个TextInput对象，用于处理输入
    private Coroutine moveCoroutine; // 保存协程的引用
    private bool isMoving = false; // 标记是否正在移动

    void Awake()
    {
        //声明一个TextInput对象，如果没有则创建一个新的
        txt ??= new TextInput();
    }
    void OnEnable()
    {
        txt.Enable();
        txt.Scene.SentMessage.performed += OnSentMessage;
    }

    void OnDisable()
    {
        // 在禁用时取消订阅事件
        txt.Scene.SentMessage.performed -= OnSentMessage;
        txt.Disable();
    }

    void Start()
    {
        // 初始状态不移动，等待按空格键开始移动
        Debug.Log("准备就绪，按空格键开始移动");
    }

    //协程方法:实例对象每帧向前移动
    IEnumerator ExampleCoroutine()
    {
        //每帧让当前对象向前移动
        while (true)
        {
            this.transform.Translate(3f * Time.deltaTime * transform.forward);
            yield return null;
        }
    }

    // 处理按下空格键的事件
    void OnSentMessage(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            if (!isMoving)
            {
                // 如果当前没有移动，则开始移动
                moveCoroutine = StartCoroutine(ExampleCoroutine());
                isMoving = true;
                Debug.Log("开始移动");
            }
            else
            {
                // 如果当前正在移动，则停止移动
                if (moveCoroutine != null)
                {
                    StopCoroutine(moveCoroutine);
                    moveCoroutine = null;
                    isMoving = false;
                    Debug.Log("停止移动");
                }
            }
        }
    }
}
