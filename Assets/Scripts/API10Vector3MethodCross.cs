using UnityEngine;

public class API10Vector3MethodCorss : MonoBehaviour
{

    // 目标物体，用于获取目标方向
    [SerializeField] private Transform target;

    //旋转的角度
    [SerializeField] private float angle = 10;


    void Update()
    {
        // 1. 获取当前方向与目标方向（通常为向量方向，需归一化）
        Vector3 currentDir = transform.forward.normalized;  // 当前前向向量（归一化）
        Vector3 targetDir = (target.position - transform.position).normalized;  // 目标方向向量（归一化）

        // 2. 计算叉积得到旋转轴
        Vector3 rotationAxis = Vector3.Cross(currentDir, targetDir).normalized;

        // 3. 两个单位向量的夹角 = 单位向量的点积的反余弦，然后转化为角度
        // angle = Mathf.Acos(Vector3.Dot(currentDir, targetDir)) * Mathf.Rad2Deg;

        // 4. 使用叉积结果（旋转轴）进行旋转（示例：使用Quaternion旋转）
        if (angle > 0.1f)
        {  // 避免微小角度抖动
            transform.rotation = Quaternion.AngleAxis(angle, rotationAxis) * transform.rotation;
        }

        // 可视化旋转轴（场景中显示蓝色向量）
        Debug.DrawLine(transform.position, transform.position + rotationAxis, Color.blue);
    }
}
