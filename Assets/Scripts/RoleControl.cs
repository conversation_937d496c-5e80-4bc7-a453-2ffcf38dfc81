using UnityEngine;
using UnityEngine.InputSystem;


public class RoleControl : MonoBehaviour
{
    public TextInput textInput;
    public float moveSpeed = 5f; // 移动速度
    private Vector2 moveInput = Vector2.zero; // 保存移动输入

    void OnEnable()
    {
        // 如果textInput为空，则创建新实例
        textInput ??= new TextInput();

        // 启用输入系统
        textInput.Enable();

        // 绑定移动事件
        textInput.Player.Move.performed += OnMove;
        textInput.Player.Move.canceled += OnMoveCancel;
    }
    void OnDisable()
    {
        if (textInput != null)
        {
            textInput.Player.Move.performed -= OnMove;
            textInput.Player.Move.canceled -= OnMoveCancel;
            textInput.Disable();
        }
    }

    void Update()
    {
        // 持续移动游戏对象
        if (moveInput != Vector2.zero)
        {
            Vector3 move = new Vector3(moveInput.x, 0, moveInput.y) * moveSpeed * Time.deltaTime;
            transform.Translate(move, Space.World);
        }
    }

    // 移动事件
    void OnMove(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }

    void OnMoveCancel(InputAction.CallbackContext context)
    {
        moveInput = Vector2.zero;
    }
}
