using UnityEngine;

public class API01EventFunc : MonoBehaviour
{
    // // 脚本实例被加载时调用，用于初始化变量
    // void Awake()
    // {
    //     Debug.Log("Awake");
    // }
    //
    // // 脚本实例被启用时调用，用于启用脚本
    // void OnEnable()
    // {
    //     Debug.Log("OnEnable");
    // }
    //
    // // 脚本实例被初始化时调用，用于初始化脚本逻辑
    // void Start()
    // {
    //     Debug.Log("Start");
    // }
    //
    // // 脚本实例被固定更新时调用，用于物理相关计算
    // void FixedUpdate()
    // {
    //     Debug.Log("FixedUpdate");
    // }
    //
    // // 脚本实例被每帧更新时调用，用于游戏逻辑处理
    // void Update()
    // {
    //     Debug.Log("Update");
    // }
    //
    // // 脚本实例被每帧更新后调用，用于摄像机跟随和控制、对骨骼附加武器，特效等、在物体位置旋转修正
    // void LateUpdate()
    // {
    //     Debug.Log("LateUpdate");
    // }
    //
    // // 脚本实例被暂停时调用，用于处理暂停相关逻辑
    // void OnApplicationPause(bool pause)
    // {
    //     Debug.Log("OnApplicationPause: " + pause);
    // }
    //
    // // 脚本实例被禁用时调用，用于处理禁用相关逻辑
    // void OnDisable()
    // {
    //     Debug.Log("OnDisable");
    // }
    //
    // // 脚本实例被退出时调用，用于处理退出相关逻辑
    // void OnApplicationQuit()
    // {
    //     Debug.Log("OnApplicationQuit");
    // }
    //
    // // 脚本实例被销毁时调用，用于处理销毁相关逻辑
    // void OnDestroy()
    // {
    //     Debug.Log("OnDestroy");
    // }
    //
    // // 脚本实例被聚焦时调用，用于处理聚焦相关逻辑
    // void OnApplicationFocus(bool focus)
    // {
    //     Debug.Log("OnApplicationFocus: " + focus);
    // }
}
