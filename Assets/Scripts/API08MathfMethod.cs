using UnityEngine;
using UnityEngine.InputSystem;

public class API08MathfMethod : MonoBehaviour
{
    [SerializeField] private float clampNum = 10f;
    [SerializeField] private float minNum = 0.5f;
    [SerializeField] private float maxNum = 100f;
    TextInput txt;

    void Awake()
    {
        txt ??= new TextInput();
    }
    void OnEnable()
    {
        txt.Enable();
        txt.Scene.SentMessage.performed += OnSentMessage;
    }
    void OnDisable()
    {
        txt.Scene.SentMessage.performed -= OnSentMessage;
        txt.Disable();
    }

    void Start()
    {
        //返回小于或等于f的最大整数
        Debug.Log("取最大整数，返回float：" + Mathf.Floor(10.5f));
        Debug.Log("取最大整数，返回int：" + Mathf.FloorToInt(10.5f));

        //返回大于或等于f的最小整数
        Debug.Log("取最小整数，返回float:" + Mathf.Ceil(10.5f));
        Debug.Log("取最小整数，返回int:" + Mathf.CeilToInt(10.5f));

        //四舍五入
        Debug.Log("四舍五入，返回float:" + Mathf.Round(10.6f));
        Debug.Log("四舍五入，返回int:" + Mathf.RoundToInt(10.6f));

    }

    void OnSentMessage(InputAction.CallbackContext context)
    {
        if (context.performed)
            //限制在min和max之间
            Debug.Log("限制在min和max之间:" + Mathf.Clamp(clampNum, minNum, maxNum));
    }
}
