using UnityEngine;

public class API08MathfLerpSmooth : MonoBehaviour
{
    [SerializeField] private float t = 0f;
    [SerializeField] private float length = 10f;
    [SerializeField] private float speed = 3f;

    private Vector3 startPosition;

    void Start()
    {
        startPosition = transform.position;
    }

    void Update()
    {
        if (t > 8 * length)
            return;


        // 使用PingPong函数使currentValue在0和length之间来回变化
        float currentValue = Mathf.Repeat(t, length);

        // 将currentValue应用到物体位置上，使其沿Z轴移动
        transform.position = startPosition + Vector3.forward * currentValue;

        // 更新t值
        t += Time.deltaTime * speed;
    }
}
