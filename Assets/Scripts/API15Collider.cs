using UnityEngine;
using UnityEngine.InputSystem;

public class API15Collider : MonoBehaviour
{
    // 声明Rigidbody组件
    private Rigidbody rb;
    // 声明新输入系统
    private TextInput txt;
    // 移动速度
    [SerializeField] private float moveSpeed = 3f;
    // 旋转速度
    [SerializeField] private float rotationSpeed = 10f;
    // 保存移动输入
    Vector2 moveInput = Vector2.zero;

    // 新输入系统
    void Awake()
    {
        txt ??= new TextInput();
    }
    // 启用新输入系统
    void OnEnable()
    {
        txt.Enable();
        // 绑定移动事件
        txt.Player.Move.performed += OnMove;
        // 绑定移动取消事件
        txt.Player.Move.canceled += OnMoveCancel;
    }

    // 禁用新输入系统
    void OnDisable()
    {
        txt.Disable();
        txt.Player.Move.performed -= OnMove;
        txt.Player.Move.canceled -= OnMoveCancel;
    }
    // 初始化Rigidbody组件
    void Start()
    {
        rb = GetComponent<Rigidbody>();
        // 让角色只能在Y轴上旋转
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;
    }

    void FixedUpdate()
    {
        // 持续移动游戏对象
        if (moveInput != Vector2.zero)
        {
            // 计算移动向量
            Vector3 move = moveSpeed * Time.fixedDeltaTime * new Vector3(moveInput.x, 0, moveInput.y);

            // 让角色转向与移动方向一致
            // 第一步：获得移动方向
            Vector3 moveDirection = new Vector3(moveInput.x, 0, moveInput.y).normalized;

            // 第二步：获得与移动方向一致的目标旋转
            Quaternion targetRotation = Quaternion.LookRotation(moveDirection);

            // 第三步：从当前旋转平滑到目标旋转
            Quaternion newRotation = Quaternion.Slerp(rb.rotation, targetRotation, rotationSpeed * Time.fixedDeltaTime);

            // 移动游戏对象
            rb.Move(rb.position + move, newRotation);
        }
    }

    void OnMove(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }

    void OnMoveCancel(InputAction.CallbackContext context)
    {
        moveInput = Vector2.zero;
    }

    void OnTriggerEnter(Collider other)
    {
        Debug.Log("触发器进入");
    }

    void OnTriggerExit(Collider other)
    {
        Debug.Log("触发器退出");
    }

    void OnTriggerStay(Collider other)
    {
        Debug.Log("触发器停留");
    }
}
