using UnityEngine;

public class API05MonoBehaviourInvokeOther : MonoBehaviour
{
    // 添加对API04Component的引用
    private API04Component api04Component;

    void Start()
    {
        // 获取API04Component组件
        api04Component = GetComponent<API04Component>();

        // 如果当前游戏对象上没有API04Component组件，尝试查找场景中的其他对象
        if (api04Component == null)
        {
            api04Component = FindAnyObjectByType<API04Component>();

            // 如果场景中没有API04Component，输出警告
            if (api04Component == null)
            {
                Debug.LogWarning("场景中没有找到API04Component组件！");
                return;
            }
        }

        //直接调用API04Component中的PrintMessage方法
        api04Component.PrintMessage();

        //使用SendMessage调用
        api04Component.gameObject.SendMessage("PrintMessage");

        // Invoke调用API04Component中的PrintMessage方法
        Invoke("CallAPI04PrintMessage", 3f);
    }

    // // 调用API04Component中的PrintMessage方法
    public void CallAPI04PrintMessage()
    {
        if (api04Component != null)
        {
            Debug.Log("即将调用API04Component中的PrintMessage方法");
            api04Component.PrintMessage();
        }
        else
        {
            Debug.LogError("API04Component组件不存在，无法调用其方法");
        }
    }
}
