using UnityEngine;
using UnityEngine.InputSystem;

public class RoleControl1 : MonoBehaviour
{
    // 声明PlayerInput组件
    PlayerInput playerInput;
    Vector2 moveInput = Vector2.zero; // 保存移动输入
    public float moveSpeed = 5f; // 移动速度，可在Inspector调整
    public float jumpForce = 5f; // 跳跃力度
    private Rigidbody rb; // 刚体组件
    private bool isGrounded = true; // 是否在地面

    // 生命周期方法，在脚本启用时，绑定Jump和Move动作
    void OnEnable()
    {
        // 获取PlayerInput组件
        playerInput = GetComponent<PlayerInput>();
        // 将OnActionTriggered方法绑定到Input System的Actions中的动作的performed事件
        playerInput.onActionTriggered -= OnActionTriggered;
        playerInput.onActionTriggered += OnActionTriggered;
        rb = GetComponent<Rigidbody>();
    }

    // 生命周期方法，在脚本禁用时，解除Jump和Move动作的绑定
    void OnDisable()
    {
        // 解除OnActionTriggered方法绑定到Input System的Actions中的动作的performed事件
        playerInput.onActionTriggered -= OnActionTriggered;
    }

    void Update()
    {
        // 按住移动键时持续移动
        if (moveInput != Vector2.zero)
        {
            Vector3 move = new Vector3(moveInput.x, 0, moveInput.y) * moveSpeed * Time.deltaTime;
            transform.Translate(move, Space.World);
        }
    }

    // 当Input System的Actions中的动作被触发时，调用OnActionTriggered方法
    void OnActionTriggered(InputAction.CallbackContext context)
    {
        if (context.phase == InputActionPhase.Performed)
            if (context.action.name == "Jump")
            {
                OnJump(context);
            }
            else if (context.action.name == "Move")
            {
                OnMove(context);
            }

        if (context.phase == InputActionPhase.Canceled)
        {
            if (context.action.name == "Move")
                moveInput = Vector2.zero;
        }
    }

    // 跳跃
    void OnJump(InputAction.CallbackContext context)
    {
        if (isGrounded && rb != null)
        {
            rb.AddForce(Vector3.up * jumpForce, ForceMode.Impulse);
            isGrounded = false;
            Debug.Log("Female Jump");
        }
    }
    // 移动
    void OnMove(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }

    // 检测是否落地
    void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.CompareTag("Ground"))
        {
            isGrounded = true;
        }
    }
}
