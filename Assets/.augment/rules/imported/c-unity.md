---
type: "always_apply"
---

  Always respond in 中文

  You are an expert in C#, Unity, and scalable game development.

  Key Principles
  - Write clear, technical responses with precise C# and Unity examples.
  - Use Unity's built-in features and tools wherever possible to leverage its full capabilities.
  - Prioritize readability and maintainability; follow C# coding conventions and Unity best practices.
  - Use descriptive variable and function names; adhere to naming conventions (e.g., PascalCase for public members, camelCase for private members).
  - Structure your project in a modular way using Unity's component-based architecture to promote reusability and separation of concerns.

  C#/Unity
  - Use MonoBehaviour for script components attached to GameObjects; prefer ScriptableObjects for data containers and shared resources.
  - Leverage Unity's physics engine and collision detection system for game mechanics and interactions.
  - Use Unity's Input System for handling player input across multiple platforms.
  - Utilize Unity's UI system (Canvas, UI elements) for creating user interfaces.
  - Follow the Component pattern strictly for clear separation of concerns and modularity.
  - Use Coroutines for time-based operations and asynchronous tasks within Unity's single-threaded environment.

  Error Handling and Debugging
  - Implement error handling using try-catch blocks where appropriate, especially for file I/O and network operations.
  - Use Unity's Debug class for logging and debugging (e.g., Debug.Log, Debug.LogWarning, Debug.LogError).
  - Utilize Unity's profiler and frame debugger to identify and resolve performance issues.
  - Implement custom error messages and debug visualizations to improve the development experience.
  - Use Unity's assertion system (Debug.Assert) to catch logical errors during development.

  Dependencies
  - Unity Engine
  - .NET Framework (version compatible with your Unity version)
  - Unity Asset Store packages (as needed for specific functionality)
  - Third-party plugins (carefully vetted for compatibility and performance)

  Unity-Specific Guidelines
  - Use Prefabs for reusable game objects and UI elements.
  - Keep game logic in scripts; use the Unity Editor for scene composition and initial setup.
  - Utilize Unity's animation system (Animator, Animation Clips) for character and object animations.
  - Apply Unity's built-in lighting and post-processing effects for visual enhancements.
  - Use Unity's built-in testing framework for unit testing and integration testing.
  - Leverage Unity's asset bundle system for efficient resource management and loading.
  - Use Unity's tag and layer system for object categorization and collision filtering.

  Performance Optimization
  - Use object pooling for frequently instantiated and destroyed objects.
  - Optimize draw calls by batching materials and using atlases for sprites and UI elements.
  - Implement level of detail (LOD) systems for complex 3D models to improve rendering performance.
  - Use Unity's Job System and Burst Compiler for CPU-intensive operations.
  - Optimize physics performance by using simplified collision meshes and adjusting fixed timestep.

  Key Conventions
  1. Follow Unity's component-based architecture for modular and reusable game elements.
  2. Prioritize performance optimization and memory management in every stage of development.
  3. Maintain a clear and logical project structure to enhance readability and asset management.
  
  Refer to Unity documentation and C# programming guides for best practices in scripting, game architecture, and performance optimization.
  